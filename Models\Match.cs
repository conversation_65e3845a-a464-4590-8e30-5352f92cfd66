using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm0446.Models
{
    public class Match
    {
        [Key]
        public string match_id { get; set; }
        
        [ForeignKey("Sport")]
        public int sport_id { get; set; }
        public DateTime match_date { get; set; }
        public TimeSpan match_time { get; set; }
        public decimal match_price { get; set; }
        public int match_quantity { get; set; }
        public string stadium { get; set; }
        public string team1 { get; set; }
        public string team2 { get; set; }

        public virtual Sport Sport { get; set; }
        public virtual ICollection<BuyDetail> BuyDetails { get; set; }
    }
}
