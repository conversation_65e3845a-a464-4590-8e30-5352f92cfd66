using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm0446.Models
{
    public class BuyDetail
    {
        [Key]
        [Column(Order = 0)]
        [ForeignKey("Buy")]
        public string buy_id { get; set; }
        
        [Key]
        [Column(Order = 1)]
        [ForeignKey("Match")]
        public string match_id { get; set; }
        
        public int quantity { get; set; }

        public virtual Buy Buy { get; set; }
        public virtual Match Match { get; set; }
    }
}
