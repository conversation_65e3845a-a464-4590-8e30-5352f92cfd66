using System.ComponentModel.DataAnnotations;

namespace Midterm0446.Models
{
    public class Sport
    {
        [Key]
        public int sport_id { get; set; }
        public string sport_name { get; set; }
        public DateTime start_date { get; set; }
        public string poster { get; set; }
        public string host_country { get; set; }

        public virtual ICollection<Match> Matches { get; set; }
    }
}
