using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Midterm0446.Models
{
    public class Buy
    {
        [Key]
        public string buy_id { get; set; }
        public DateTime buy_date { get; set; }
        
        [ForeignKey("Customer")]
        public string cus_id { get; set; }

        public virtual Customer Customer { get; set; }
        public virtual ICollection<BuyDetail> BuyDetails { get; set; }
    }
}
