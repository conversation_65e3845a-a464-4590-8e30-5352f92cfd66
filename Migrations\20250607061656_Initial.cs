﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Midterm0446.Migrations
{
    /// <inheritdoc />
    public partial class Initial : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Customers",
                columns: table => new
                {
                    cus_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    cus_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    password = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    address = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    phone = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Customers", x => x.cus_id);
                });

            migrationBuilder.CreateTable(
                name: "Sports",
                columns: table => new
                {
                    sport_id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    sport_name = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    start_date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    poster = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    host_country = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Sports", x => x.sport_id);
                });

            migrationBuilder.CreateTable(
                name: "Buys",
                columns: table => new
                {
                    buy_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    buy_date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    cus_id = table.Column<string>(type: "nvarchar(450)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Buys", x => x.buy_id);
                    table.ForeignKey(
                        name: "FK_Buys_Customers_cus_id",
                        column: x => x.cus_id,
                        principalTable: "Customers",
                        principalColumn: "cus_id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Matches",
                columns: table => new
                {
                    match_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    sport_id = table.Column<int>(type: "int", nullable: false),
                    match_date = table.Column<DateTime>(type: "datetime2", nullable: false),
                    match_time = table.Column<TimeSpan>(type: "time", nullable: false),
                    match_price = table.Column<decimal>(type: "decimal(18,2)", nullable: false),
                    match_quantity = table.Column<int>(type: "int", nullable: false),
                    stadium = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    team1 = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    team2 = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Matches", x => x.match_id);
                    table.ForeignKey(
                        name: "FK_Matches_Sports_sport_id",
                        column: x => x.sport_id,
                        principalTable: "Sports",
                        principalColumn: "sport_id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "BuyDetails",
                columns: table => new
                {
                    buy_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    match_id = table.Column<string>(type: "nvarchar(450)", nullable: false),
                    quantity = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BuyDetails", x => new { x.buy_id, x.match_id });
                    table.ForeignKey(
                        name: "FK_BuyDetails_Buys_buy_id",
                        column: x => x.buy_id,
                        principalTable: "Buys",
                        principalColumn: "buy_id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_BuyDetails_Matches_match_id",
                        column: x => x.match_id,
                        principalTable: "Matches",
                        principalColumn: "match_id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_BuyDetails_match_id",
                table: "BuyDetails",
                column: "match_id");

            migrationBuilder.CreateIndex(
                name: "IX_Buys_cus_id",
                table: "Buys",
                column: "cus_id");

            migrationBuilder.CreateIndex(
                name: "IX_Matches_sport_id",
                table: "Matches",
                column: "sport_id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "BuyDetails");

            migrationBuilder.DropTable(
                name: "Buys");

            migrationBuilder.DropTable(
                name: "Matches");

            migrationBuilder.DropTable(
                name: "Customers");

            migrationBuilder.DropTable(
                name: "Sports");
        }
    }
}
