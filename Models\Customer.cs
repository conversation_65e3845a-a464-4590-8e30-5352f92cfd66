using System.ComponentModel.DataAnnotations;

namespace Midterm0446.Models
{
    public class Customer
    {
        [Key]
        public string cus_id { get; set; }
        public string cus_name { get; set; }
        public string password { get; set; }
        public string address { get; set; }
        public string phone { get; set; }

        public virtual ICollection<Buy> Buys { get; set; }
    }
}
