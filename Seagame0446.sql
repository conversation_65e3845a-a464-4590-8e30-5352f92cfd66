-- Insert data for Sports table
INSERT INTO Sports (sport_id, sport_name, start_date, poster, host_country) VALUES
(1, 'Football', '2025-03-01', '/Content/images/football.jpg', 'Vietnam'),
(2, 'Basketball', '2025-03-05', '/Content/images/basketball.jpg', 'Vietnam'),
(3, '<PERSON><PERSON><PERSON>', '2025-03-10', '/Content/images/badminton.jpg', 'Vietnam'),
(4, 'Swimming', '2025-04-01', '/Content/images/swimming.jpg', 'Vietnam'),
(5, 'Table Tennis', '2025-04-10', '/Content/images/tabletennis.jpg', 'Vietnam');

-- Insert data for Matches table
INSERT INTO Matches (match_id, sport_id, match_date, match_time, match_price, match_quantity, stadium, team1, team2) VALUES
('M1X1', 1, '2025-03-02', '16:30', 150000, 0, 'My Dinh Stadium', 'Vietnam', 'Thailand'),
('M2X1', 1, '2025-03-25', '20:00', 120000, 50, 'Hang Day Stadium', 'Malaysia', 'Indonesia'),
('M2X1', 2, '2025-03-20', '19:00', 100000, 30, 'Quan khu 7 Stadium', 'Philippines', 'Singapore'),
('M3X1', 3, '2025-03-18', '17:00', 80000, 0, 'Trinh Hoai Duc Stadium', 'Vietnam', 'Malaysia'),
('M3X2', 3, '2025-03-19', '16:00', 90000, 25, 'Gia Lam Stadium', 'Indonesia', 'Thailand');

-- Insert data for Customers table
INSERT INTO Customers (cus_id, cus_name, password, address, phone) VALUES
('C001', 'Nguyen Van A', 'password123', 'Ha Noi', '0123456789'),
('C002', 'Tran Thi B', 'password456', 'Ho Chi Minh', '0987654321'),
('C003', 'Le Van C', 'password789', 'Da Nang', '0111222333');

-- Insert data for Buys table
INSERT INTO Buys (buy_id, buy_date, cus_id) VALUES
('B001', '2025-02-15', 'C001'),
('B002', '2025-02-20', 'C002'),
('B003', '2025-02-25', 'C003');

-- Insert data for BuyDetails table
INSERT INTO BuyDetails (buy_id, match_id, quantity) VALUES
('B001', 'M1X1', 2),
('B001', 'M2X1', 1),
('B002', 'M2X1', 3),
('B002', 'M3X1', 2),
('B003', 'M3X2', 1);
